using System;
using Cysharp.Threading.Tasks;
using Fusion;
using Game.Views.Levels;
using Game.Views.Monsters;
using Game.Views.PlayerEffects;
using Game.Views.Players;
using MessagePipe;
using Modules.Core;
using Modules.Network;
using UnityEngine;
using UnityEngine.AI;
using VContainer;
using Monsters_MonsterDamageArgs = Game.Views.Monsters.MonsterDamageArgs;
using Random = UnityEngine.Random;

namespace Game.Controllers.Monsters
{
    public class SpiderBossBrainController : NetworkActor
    {
        [SerializeField] private MonsterActor monster;
        [SerializeField] private NavMeshAgent navMeshAgent;

        private LevelModel levelModel;
        private MonstersConfig monstersConfig;
        private PlayersModel playersModel;
        private IAudioClient audioClient;
        private PlayerEffectsManager effectsManager;
        
        private float nextRotateToTargetTime;
        private float nextDamageReceiveTime;
        private float nextIdleOrWalkSwitchTime;
        private float nextVoiceTime;
        private float nextAttackTime;

        private bool HasTarget => targetPlayer != null;
        private bool IsAttacking => Time.time < nextAttackTime;
        private bool IsDamageReceiving => Time.time < nextDamageReceiveTime;
        private bool IsAlive => monster.IsAlive;
        private float ChaseSpeed => monster.ChaseSpeed;
        private byte AttackDamage => monster.AttackDamage;
        private float PatrolSpeed => monster.PatrolSpeed;
        private bool isStuck;
        private float nextPlayerCheckTime;
        private float nextPathUpdateTime;
        private const float pathUpdateInterval = 0.2f;

        private float patrolBoundsXMin, patrolBoundsXMax, patrolBoundsZMin, patrolBoundsZMax, patrolBoundsYMin, patrolBoundsYMax;
        private bool hasPatrolArea;

        private PlayerActor targetPlayer;
        
        private MonstersConfig.Data monsterData;
        
        private MonsterState State
        {
            get => monster.State;
            set
            {
                previousFrameState = monster.State;
                monster.State = value;
            }
        }

        private MonsterState previousFrameState = MonsterState.Idle;

        [Inject]
        private void Construct(
            LevelModel levelModel,
            MonstersConfig monstersConfig,
            PlayersModel playersModel,
            ISubscriber<Monsters_MonsterDamageArgs> damageSubscriber,
            IAudioClient audioClient,
            PlayerEffectsManager effectsManager)
        {
            this.levelModel = levelModel;
            this.monstersConfig = monstersConfig;
            this.playersModel = playersModel;
            this.audioClient = audioClient;
            this.effectsManager = effectsManager;

            damageSubscriber.Subscribe(HandleDamage).AddTo(destroyCancellationToken);
        }

        public override void Render()
        {
            base.Render();
            if (IsAlive)
            {
                PlayRandomVoice();
            }

            if (!Runner.IsSharedModeMasterClient)
            {
                return;
            }
            
            if (monster.StateAuthority == PlayerRef.Invalid || monster.StateAuthority == PlayerRef.None && Time.time >= nextPlayerCheckTime)
            {
                nextPlayerCheckTime = Time.time + 2f;
                Debug.LogWarning("[MineMonsterBrainController]: Object lost State Authority! Requesting transfer to master client...");
                TransferAuthority().Forget();
            }
        }

        public override void FixedUpdateNetwork()
        {
            base.FixedUpdateNetwork();
            ScanTarget();
            UpdateStates();
            TryPlaceOnNavMesh();
        }

        public override void Spawned()
        {
            base.Spawned();

            if (HasStateAuthority)
            {
                if (levelModel.Level.monsters != null && monster.MonsterDataIndex < levelModel.Level.monsters.Count)
                {
                    navMeshAgent.enabled = false;
                    var monsterSpawnData = levelModel.Level.monsters[monster.MonsterDataIndex];
                    monster.Teleport(monsterSpawnData.spawn);
                    hasPatrolArea = monsterSpawnData.HasPatrolArea;
                    navMeshAgent.enabled = true;
                    if (hasPatrolArea)
                    {
                        patrolBoundsXMin = monsterSpawnData.patrolCenter.x - 0.5f * Math.Abs(monsterSpawnData.patrolArea.x);
                        patrolBoundsXMax = monsterSpawnData.patrolCenter.x + 0.5f * Math.Abs(monsterSpawnData.patrolArea.x);

                        patrolBoundsZMin = monsterSpawnData.patrolCenter.z - 0.5f * Math.Abs(monsterSpawnData.patrolArea.z);
                        patrolBoundsZMax = monsterSpawnData.patrolCenter.z + 0.5f * Math.Abs(monsterSpawnData.patrolArea.z);

                        patrolBoundsYMin = monsterSpawnData.patrolCenter.y - 0.5f * Math.Abs(monsterSpawnData.patrolArea.y);
                        patrolBoundsYMax = monsterSpawnData.patrolCenter.y + 0.5f * Math.Abs(monsterSpawnData.patrolArea.y);
                    }
                    
                    if (monstersConfig.TryGetMonsterData(monster.AvatarId, out var data))
                    {
                        monsterData = data;
                    }
                }
            }
        }

        private void UpdateStates()
        {
            if (!HasTarget)
            {
                if (Time.time >= nextIdleOrWalkSwitchTime)
                {
                    nextIdleOrWalkSwitchTime = Time.time + Random.Range(1f, 15f);
                    State = State == MonsterState.Idle ? MonsterState.Patrol : MonsterState.Idle;
                }

                if (State == MonsterState.Patrol)
                {
                    Patrol();
                }
                else if (State == MonsterState.Idle)
                {
                    navMeshAgent.ResetPath();
                }
            }
            else
            {
                State = MonsterState.Chase;
                if (IsInRange())
                {
                    Attack();
                }
                else
                {
                    Chase();
                }
            }

            HandleTransitions();
        }

        private void Chase()
        {
            if (!navMeshAgent.isOnNavMesh)
            {
                return;
            }

            navMeshAgent.speed = ChaseSpeed;
            if (Time.time >= nextPathUpdateTime)
            {
                navMeshAgent.SetDestination(targetPlayer.transform.position);
                nextPathUpdateTime = Time.time + pathUpdateInterval;
            }
        }

        private bool IsTargetInPatrolArea(Transform target)
        {
            if (!hasPatrolArea)
            {
                return true;
            }

            if (target == null)
            {
                return false;
            }

            var targetPos = target.position;

            return targetPos.x >= patrolBoundsXMin &&
                   targetPos.x <= patrolBoundsXMax &&
                   targetPos.z >= patrolBoundsZMin &&
                   targetPos.z <= patrolBoundsZMax &&
                   targetPos.y >= patrolBoundsYMin &&
                   targetPos.y <= patrolBoundsYMax;
        }

        private void HandleTransitions()
        {
            // Transitions
            // If we go from something else to Chase, start chase sound
            if (previousFrameState != MonsterState.Chase && State == MonsterState.Chase)
            {
                SetScreamRpc(true);
            }

            // If we go from chase to something else
            if (previousFrameState == MonsterState.Chase && State != MonsterState.Chase)
            {
                SetScreamRpc(false);
            }
        }
        
        private bool IsInRange()
        {
            if (monsterData == null) return false;
            return Vector3.Distance(targetPlayer.transform.position, monster.transform.position) < monsterData.AttackMinDistance;
        }

        private void ScanTarget()
        {
            if (targetPlayer != null)
            {
                if (!IsPlayerTargetable(targetPlayer) || Vector3.Distance(targetPlayer.transform.position, monster.transform.position) > monstersConfig.ScanPlayerRadius)
                {
                    if (targetPlayer != null)
                    {
                        SetNewTargetRpc(null);
                    }
                }
            }
            else
            {
                if (playersModel.TryFindClosestPlayer(monster.transform.position, monstersConfig.ScanPlayerRadius, IsPlayerTargetable, out var player, 10))
                {
                    if (targetPlayer != player)
                    {
                        SetNewTargetRpc(player);
                    }
                }
            }
        }

        private async UniTaskVoid TransferAuthority()
        {
            navMeshAgent.enabled = false;
            navMeshAgent.updateRotation = false;
            var result = await Object.RequestStateAuthorityAsync(DespawnCancellationToken);
            if (result)
            {
                if (targetPlayer != null)
                {
                    var direction = (targetPlayer.transform.position - monster.transform.position).normalized;
                    monster.transform.rotation = Quaternion.LookRotation(direction);
                }
        
                monster.Teleport(monster.transform.position);
                navMeshAgent.enabled = true;
                navMeshAgent.updateRotation = true;
            }
        }

        private bool IsPlayerTargetable(PlayerActor player)
        {
            if (levelModel.LevelConfig.IsInfectedWhenDead)
            {
                return player.IsAlive && !player.IsTagged.Value;
            }

            if (hasPatrolArea && !IsTargetInPatrolArea(player.transform))
            {
                return false;
            }

            return player.IsAlive;
        }

        private void Patrol()
        {
            if (!navMeshAgent.isOnNavMesh)
            {
                return;
            }

            navMeshAgent.speed = PatrolSpeed;
            if (navMeshAgent.pathStatus == NavMeshPathStatus.PathPartial || navMeshAgent.remainingDistance < 0.5f)
            {
                navMeshAgent.SetDestination(GetRandomNavMeshPoint());
            }
        }

        private void Attack()
        {
            if (nextAttackTime == 0)
            {
                nextAttackTime = Time.time + monstersConfig.InitialAttackDelay;
                return;
            }

            if (IsAttacking)
            {
                return;
            }

            AttackDelayed(2f).Forget();
            nextAttackTime = Time.time + monstersConfig.AttackInterval;
        }

        private async UniTaskVoid AttackDelayed(float delay)
        {
            effectsManager.RenderSpiderBossDamageEffect(1f);
            SetAttackAudioRpc();
            await UniTask.WaitForSeconds(delay, cancellationToken: destroyCancellationToken);
            targetPlayer.SetDamageByMonsterRpc(AttackDamage);
        }

        [Rpc(RpcSources.StateAuthority, RpcTargets.All, Channel = RpcChannel.Unreliable)]
        private void SetScreamRpc(bool scream)
        {
            if (monsterData != null)
            {
                if (scream)
                {
                    monster.PlayAudio(monsterData.ChaseSoundKey, true);
                }
                else
                {
                    audioClient.Stop(monsterData.ChaseSoundKey);
                }
            }
        }

        [Rpc(RpcSources.StateAuthority, RpcTargets.All, Channel = RpcChannel.Unreliable)]
        private void SetAttackAudioRpc()
        {
            if (monsterData != null)
            {
                monster.PlayAudio(monsterData.AttackSoundKey, true);
            }
        }

        [Rpc(RpcSources.All, RpcTargets.All)]
        private void SetNewTargetRpc(PlayerActor playerActor)
        {
            targetPlayer = playerActor;
            if (playersModel.LocalPlayer.Value == targetPlayer && !HasStateAuthority)
            {
                TransferAuthority().Forget();
            }
        }

        private void ReceiveDamage()
        {
            if (IsDamageReceiving)
            {
                return;
            }

            nextDamageReceiveTime = Time.time + 0.25f;
        }

        private void TryPlaceOnNavMesh()
        {
            if (!navMeshAgent.enabled)
            {
                return;
            }

            if (navMeshAgent.isOnNavMesh)
            {
                return;
            }

            if (NavMesh.SamplePosition(monster.transform.position, out var hit, 10f, NavMesh.AllAreas))
            {
                monster.Teleport(hit.position);
            }
        }

        private Vector3 GetRandomNavMeshPoint()
        {
            Vector3 searchOrigin;
            float searchRadius;

            if (hasPatrolArea)
            {
                searchRadius = 1f;
                searchOrigin = new Vector3(
                    Random.Range(patrolBoundsXMin, patrolBoundsXMax),
                    transform.position.y,
                    Random.Range(patrolBoundsZMin, patrolBoundsZMax)
                );

                if (NavMesh.SamplePosition(searchOrigin, out var hit, searchRadius, NavMesh.AllAreas))
                {
                    return hit.position;
                }
            }
            else
            {
                searchOrigin = transform.position;
                searchRadius = 10f;
                var randomDirection = Random.insideUnitSphere * 10f;
                randomDirection += searchOrigin;

                if (NavMesh.SamplePosition(randomDirection, out var hit, searchRadius, NavMesh.AllAreas))
                {
                    return hit.position;
                }
            }

            return monster.transform.position;
        }

        private void PlayRandomVoice()
        {
            if (Time.time < nextVoiceTime)
            {
                return;
            }

            if (monsterData != null)
            {
                monster.PlayAudio(monsterData.RandomSoundKey);
            }

            nextVoiceTime = Time.time + Random.Range(5f, 15f);
        }

        private void HandleDamage(Monsters_MonsterDamageArgs args)
        {
            if (!HasStateAuthority || args.monster != monster || !IsAlive)
            {
                return;
            }

            ReceiveDamage();
        }
    }
}