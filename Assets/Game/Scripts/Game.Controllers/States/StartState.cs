using System.Threading;
using Cysharp.Threading.Tasks;
using Game.Controllers.Analytics;
using Game.Controllers.Audio;
using Game.Controllers.Backpack;
using Game.Controllers.Badges;
using Game.Controllers.Blocks;
using Game.Controllers.BlockShop;
using Game.Controllers.BuildingArea;
using Game.Controllers.BuildIslands;
using Game.Controllers.Checkpoints;
using Game.Controllers.Consumables;
using Game.Controllers.CrownMode;
using Game.Controllers.DailyChallenges;
using Game.Controllers.Effects;
using Game.Controllers.Friends;
using Game.Controllers.GadgetShop;
using Game.Controllers.GameModes;
using Game.Controllers.Grabbing;
using Game.Controllers.Guns;
using Game.Controllers.InGameRewards;
using Game.Controllers.Interactables;
using Game.Controllers.Leaderboard;
using Game.Controllers.Levels;
using Game.Controllers.Lobby;
using Game.Controllers.LocomotionArea;
using Game.Controllers.Locomotions;
using Game.Controllers.Mirror;
using Game.Controllers.Moderation;
using Game.Controllers.MoneyBag;
using Game.Controllers.Monsters;
using Game.Controllers.Network;
using Game.Controllers.Ores;
using Game.Controllers.PlayerBounds;
using Game.Controllers.Players;
using Game.Controllers.PlayerUI;
using Game.Controllers.RaceLeaderboards;
using Game.Controllers.ReportPlayers;
using Game.Controllers.Screens;
using Game.Controllers.Shop;
using Game.Controllers.VideoRecorder;
using Game.Controllers.Voxels;
using Game.Controllers.Weapons;
using Game.Controllers.Wings;
using Game.Controllers.Zombies;
using Game.Core;
using Modules.Core;
using Modules.XR;
using UnityEngine;
using VContainer;

namespace Game.Controllers.States
{
    public class StartState : StateBase
    {
        private IXRPlayer xrPlayer;
        private GameConfig gameConfig;
        private IXRInteractors xrInteractors;
        private ControllerManager controllerManager;

        [Inject]
        private void Construct(
            IXRPlayer xrPlayer,
            GameConfig gameConfig,
            IXRInteractors xrInteractors,
            ControllerManager controllerManager)
        {
            this.xrPlayer = xrPlayer;
            this.gameConfig = gameConfig;
            this.xrInteractors = xrInteractors;
            this.controllerManager = controllerManager;
        }

        public override UniTask EnterAsync(CancellationToken cancellationToken = default)
        {
            Physics.gravity = CoreConstants.WorldScaleGravity;

            xrPlayer.FadeInView(0);
            xrPlayer.SetActivePhysics(false);
            xrInteractors.ApplyRayConfig(gameConfig.DisabledRayInteractorConfig);

            // Audio
            controllerManager.GetOrCreate<AudioCollectionController>();

            // Analytics
            controllerManager.GetOrCreate<AnalyticsController>();

            // Levels
            controllerManager.GetOrCreate<LevelController>();
            controllerManager.GetOrCreate<LevelPortalController>();
            controllerManager.GetOrCreate<LevelSaveController>();
            controllerManager.GetOrCreate<LevelStartController>();
            controllerManager.GetOrCreate<LevelPublishController>();
            controllerManager.GetOrCreate<LevelSettingsController>();
            controllerManager.GetOrCreate<LevelDisabledBuildingController>();
            controllerManager.GetOrCreate<LevelMapLoadedController>();
            controllerManager.GetOrCreate<LevelSessionListController>();
            controllerManager.GetOrCreate<CreateLevelScreenController>();
            controllerManager.GetOrCreate<JoinPublicLevelScreenController>();
            controllerManager.GetOrCreate<JoinPrivateLevelScreenController>();
            controllerManager.GetOrCreate<LevelActiveLoadingStateController>();

            // Lobby
            controllerManager.GetOrCreate<LobbyController>();
            controllerManager.GetOrCreate<LobbyPortalsController>();

            // Voxels
            controllerManager.GetOrCreate<ApplySliceMapController>();
            controllerManager.GetOrCreate<DamageVoxelController>();
            controllerManager.GetOrCreate<VoxelNetworkController>();
            controllerManager.GetOrCreate<SpreadVoxelsController>();
            controllerManager.GetOrCreate<CreateVoxelController>();
            controllerManager.GetOrCreate<SliceVoxelsController>();
            controllerManager.GetOrCreate<LavaDamageVoxelController>();
            controllerManager.GetOrCreate<VoxelCameraTintController>();
            controllerManager.GetOrCreate<VoxelMapLoadedController>();
            controllerManager.GetOrCreate<SendInitialVoxelsController>();
            controllerManager.GetOrCreate<ReceiveInitialVoxelsController>();
            controllerManager.GetOrCreate<SaveSliceMapController>();
            controllerManager.GetOrCreate<VoxelTrackPlayerSubmergedController>();

            // Locomotions
            controllerManager.GetOrCreate<ArmLocomotionController>();
            controllerManager.GetOrCreate<SwimLocomotionController>();
            controllerManager.GetOrCreate<WingitLocomotionController>();
            controllerManager.GetOrCreate<LocomotionBalancerController>();
            controllerManager.GetOrCreate<BroomFlyingLocomotionController>();

            // Players
            controllerManager.GetOrCreate<PlayerNameController>();
            controllerManager.GetOrCreate<PlayerScaleController>();
            controllerManager.GetOrCreate<PlayerBadgeController>();
            controllerManager.GetOrCreate<PlayerHealthController>();
            controllerManager.GetOrCreate<PlayerJoinedController>();
            controllerManager.GetOrCreate<PlayerAvatarController>();
            controllerManager.GetOrCreate<PlayerOfflineController>();
            controllerManager.GetOrCreate<PlayerTeleportController>();
            controllerManager.GetOrCreate<PlayerRespawnController>();
            controllerManager.GetOrCreate<PlayerDayCountController>();
            controllerManager.GetOrCreate<PlayerSetDamageController>();
            controllerManager.GetOrCreate<PlayerGetDamageController>();
            controllerManager.GetOrCreate<PlayerInfectionController>();
            controllerManager.GetOrCreate<PlayerCollisionController>();
            controllerManager.GetOrCreate<PlayerLocomotionsController>();
            controllerManager.GetOrCreate<PlayerLabelConditionController>();
            controllerManager.GetOrCreate<PlayerConsumeMedicalController>();
            controllerManager.GetOrCreate<PlayerKilledZombieCountController>();
            controllerManager.GetOrCreate<PlayerDeathDropController>();

            // PlayerUI
            controllerManager.GetOrCreate<WristMenuController>();
            controllerManager.GetOrCreate<HandsMenuController>();
            controllerManager.GetOrCreate<PlayerMenuController>();
            controllerManager.GetOrCreate<InteractablesMenuController>();
            controllerManager.GetOrCreate<HomeButtonVisibilityController>();

            // Interactables
            controllerManager.GetOrCreate<CreateInteractableController>();
            controllerManager.GetOrCreate<ActiveInteractablesController>();
            controllerManager.GetOrCreate<InteractableDropTimeoutController>();

            // Grabbing
            controllerManager.GetOrCreate<GrabbingController>();

            // Guns
            controllerManager.GetOrCreate<WebShooterController>();
            controllerManager.GetOrCreate<GrappleGunController>();

            // Weapons
            controllerManager.GetOrCreate<HammerController>();

            // Wings
            controllerManager.GetOrCreate<WingsController>();

            // Consumeables
            controllerManager.GetOrCreate<ConsumeablesConsumeController>();

            // Shop
            controllerManager.GetOrCreate<CoinValidatorController>();
            controllerManager.GetOrCreate<AtmViewController>();
            controllerManager.GetOrCreate<BigShopController>();
            controllerManager.GetOrCreate<SmallShopController>();
            controllerManager.GetOrCreate<ShopInventoryInteractController>();

            // Game Modes
            controllerManager.GetOrCreate<InfectionGameModeController>();
            controllerManager.GetOrCreate<MinesPosterController>();

            // Crown Mode
            controllerManager.GetOrCreate<CrownGameController>();
            controllerManager.GetOrCreate<CrownInfoController>();
            controllerManager.GetOrCreate<CrownCatchController>();
            controllerManager.GetOrCreate<CrownPlayerController>();
            controllerManager.GetOrCreate<CrownScreenController>();
            controllerManager.GetOrCreate<CrownAwardPlayerController>();

            // Zombies
            controllerManager.GetOrCreate<ZombieSpawnController>();

            //Monsters
            controllerManager.GetOrCreate<MonsterSpawnController>();

            // Leaderboard
            controllerManager.GetOrCreate<LeaderboardController>();
            controllerManager.GetOrCreate<LeaderboardScreenController>();

            // Network
            controllerManager.GetOrCreate<NetworkSessionCloseController>();
            controllerManager.GetOrCreate<NetworkErrorHandleController>();
            controllerManager.GetOrCreate<NetworkDisconnectionController>();
            controllerManager.GetOrCreate<NetworkClientConnectionController>();

            // Moderation
            controllerManager.GetOrCreate<ModerationApplyController>();
            controllerManager.GetOrCreate<ModerationStickController>();
            controllerManager.GetOrCreate<ModerationReceiveController>();
            controllerManager.GetOrCreate<ModerationSentenceController>();
            controllerManager.GetOrCreate<ModerationSentencePanelController>();
            controllerManager.GetOrCreate<ModerationMuteTimeEndController>();
            controllerManager.GetOrCreate<ModerationPrisonScreenController>();
            controllerManager.GetOrCreate<ModerationPrisonTimeEndController>();

            // Money Bag
            controllerManager.GetOrCreate<MoneyBagController>();

            controllerManager.GetOrCreate<MenuScreenController>();
            controllerManager.GetOrCreate<ModerationScreenController>();
            controllerManager.GetOrCreate<MenuPanelController>();
            controllerManager.GetOrCreate<DailyChallengesController>();
            controllerManager.GetOrCreate<RoomInfoScreenController>();
            controllerManager.GetOrCreate<SocialLinksScreenController>();
            controllerManager.GetOrCreate<PromoCodeScreenController>();
            controllerManager.GetOrCreate<PrivateLobbyScreenController>();

            controllerManager.GetOrCreate<EffectsController>();

            //Checkpoints
            controllerManager.GetOrCreate<CheckpointsController>();
            controllerManager.GetOrCreate<CheckpointsRespawnController>();
            controllerManager.GetOrCreate<RaceLeaderboardController>();

            controllerManager.GetOrCreate<LocomotionAreaController>();

            controllerManager.GetOrCreate<BuildingAreaController>();

            controllerManager.GetOrCreate<PlayerBoundsController>();

            // Video Recorder
            controllerManager.GetOrCreate<VideoRecorderController>();

            // Ore
            controllerManager.GetOrCreate<VoxelOreSpawnController>();
            controllerManager.GetOrCreate<OreConverterController>();

            controllerManager.GetOrCreate<PlayerDayStreakController>();

            controllerManager.GetOrCreate<InfluencerScreenController>();

            controllerManager.GetOrCreate<FriendsController>();

            // Build Islands
            controllerManager.GetOrCreate<BuildIslandController>();
            controllerManager.GetOrCreate<BuildIslandControlController>();

            controllerManager.GetOrCreate<BadgesController>();

            // Report Players
            controllerManager.GetOrCreate<ReportPlayersController>();

            // Blocks
            controllerManager.GetOrCreate<BlocksMenuController>();
            controllerManager.GetOrCreate<BlockInventorySaveController>();

            // Backpack
            controllerManager.GetOrCreate<BackpackController>();

            // Gadget Shop
            controllerManager.GetOrCreate<GadgetShopController>();

            // Block Shop
            controllerManager.GetOrCreate<BlockShopController>();

            controllerManager.GetOrCreate<MirrorController>();

            controllerManager.GetOrCreate<AnnouncementScreenController>();
            controllerManager.GetOrCreate<ReviewUsScreenController>();
            
            controllerManager.GetOrCreate<InGameRewardsController>();

            return UniTask.CompletedTask;
        }
    }
}