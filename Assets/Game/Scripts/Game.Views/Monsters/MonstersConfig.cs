using System;
using System.Collections.Generic;
using Fusion;
using Game.Core.Data;
using Modules.Core;
using UnityEngine;
#if UNITY_EDITOR
using UnityEditor;
using System.Globalization;
using System.Text.RegularExpressions;
#endif

namespace Game.Views.Monsters
{
    public class MonstersConfig : Config
    {
        [Header("Monsters")]
        [SerializeField] private int scanPlayerRadius;
        [SerializeField] private float initialAttackDelay;
        [SerializeField] private float attackInterval = 0.2f;
        
        [Header("Spiders")]
        [SerializeField] private int spiderFollowPlayerRadius;
        [SerializeField] private float spiderAttackInterval = 0.2f;
        [SerializeField] private float spiderInitialAttackDelay;
        
        [SerializeField] private List<Data> dataList;
        [SerializeField] private GameObject monsterPositionPrefab;
        [SerializeField] private List<MonsterData> monsterData;

        public int ScanPlayerRadius => scanPlayerRadius;
        public float AttackInterval => attackInterval;
        public float InitialAttackDelay => initialAttackDelay;

        public int SpiderFollowPlayerRadius => spiderFollowPlayerRadius;
        public float SpiderAttackInterval => spiderAttackInterval;
        public float SpiderInitialAttackDelay => spiderInitialAttackDelay;

        public byte GetRandomAvatarId()
        {
            return dataList.RandomItem().Avatar?.Code ?? 0;
        }

        public bool TryGetAvatarPrefab(byte code, out MonsterAvatar avatar)
        {
            avatar = dataList.Find(x => x.Avatar.Code == code)?.Avatar;
            return avatar != null;
        }

        public bool TryGetRagdollPrefab(byte code, out MonsterRagdoll ragdoll)
        {
            ragdoll = dataList.Find(x => x.Avatar.Code == code)?.Ragdoll;
            return ragdoll != null;
        }

        public bool TryGetMonsterData(byte code, out Data data)
        {
            data = dataList.Find(x => x.Avatar.Code == code);
            return data != null;
        }

        public NetworkObject GetNetworkObjectPrefab(byte code, bool random = false)
        {
            if (random)
            {
                return dataList.RandomItem().MonsterPrefab;
            }

            return dataList.Find(x => x.Avatar.Code == code)?.MonsterPrefab;
        }

#if UNITY_EDITOR
        [ContextMenu("Generate Monster Data")]
        public void GenerateMonsterData()
        {
            if (monsterPositionPrefab == null) return;

            monsterData.Clear();

            var monsterPositions = monsterPositionPrefab.GetComponentsInChildren<Transform>();
            for (var i = 0; i < monsterPositions.Length; i++)
            {
                if (monsterPositions[i] == monsterPositionPrefab.transform) continue;

                var position = monsterPositions[i].position;
                var angleY = monsterPositions[i].localEulerAngles.y;

                monsterData.Add(new MonsterData(0, position, angleY, position, Vector3.zero));
            }

            EditorUtility.SetDirty(this);
        }

        [ContextMenu("Export monster data as JSON")]
        public void ExportJson()
        {
            if (monsterData == null)
            {
                Log.Debug("No monster data available to export!");
                return;
            }

            try
            {
                var wrapper = new MonstersWrapper { monsters = monsterData };
                var json = JsonUtility.ToJson(wrapper, true);

                var cleanJson = CleanJsonDecimals(json);
                Debug.Log(cleanJson);
            }
            catch (Exception ex)
            {
                Log.Error($"Error serializing checkpoint data: {ex.Message}");
            }
        }

        public string CleanJsonDecimals(string json)
        {
            return Regex.Replace(json, @"\b\d+\.\d+\b", match =>
            {
                if (float.TryParse(match.Value, NumberStyles.Float, CultureInfo.InvariantCulture, out var result))
                {
                    return result.ToString("F0", CultureInfo.InvariantCulture);
                }

                return match.Value; // Preserve original value if parsing fails
            });
        }

#endif

        [Serializable]
        public class Data
        {
            [SerializeField] private NetworkObject monsterPrefab;
            [SerializeField] private MonsterAvatar avatar;
            [SerializeField] private MonsterRagdoll ragdoll;
            [SerializeField] private byte health;
            [SerializeField] private float chaseSpeed;
            [SerializeField] private byte damage;
            [SerializeField] private float patrolSpeed;
            [SerializeField] private string chaseSoundKey;
            [SerializeField] private string attackSoundKey;
            [SerializeField] private string damageReceivedSoundKey;
            [SerializeField] private List<string> randomSoundKeys;
            [SerializeField] private float respawnDelay;
            [SerializeField] private float attackMinDistance;
            [SerializeField] private float startFollowMinDistance;
            
            public NetworkObject MonsterPrefab => monsterPrefab;
            public MonsterAvatar Avatar => avatar;
            public MonsterRagdoll Ragdoll => ragdoll;
            public byte Health => health;
            public float ChaseSpeed => chaseSpeed;
            public byte Damage => damage;
            public float PatrolSpeed => patrolSpeed;
            public string ChaseSoundKey => chaseSoundKey;
            public string AttackSoundKey => attackSoundKey;
            public string DamageReceivedSoundKey => damageReceivedSoundKey;
            public string RandomSoundKey => randomSoundKeys.RandomItem();
            public float RespawnDelay => respawnDelay;
            public float AttackMinDistance => attackMinDistance;
            public float StartFollowMinDistance => startFollowMinDistance;
        }

        [Serializable]
        public class SpawnData
        {
            [SerializeField] private byte code;
            [SerializeField] private Vector3 spawnPoint;
            [SerializeField] private float rotation;

            public byte Code => code;
            public Vector3 SpawnPoint => spawnPoint;
            public float Rotation => rotation;
        }
    }

#if UNITY_EDITOR
    [CustomEditor(typeof(MonstersConfig))]
    public class MonsterConfigEditor : Editor
    {
        public override void OnInspectorGUI()
        {
            DrawDefaultInspector();

            var config = (MonstersConfig)target;

            if (GUILayout.Button("Generate Monster Data"))
            {
                config.GenerateMonsterData();
            }

            if (GUILayout.Button("Export as JSON"))
            {
                config.ExportJson();
            }
        }
    }
#endif
}